using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// Test script for the Vehicle Proximity Selection System
/// Attach this to a GameObject in your scene to test and debug the system
/// </summary>
public class VehicleProximitySelectionTester : MonoBehaviour
{
    [Header("System References")]
    public JrsVehicleSelector vehicleSelector;
    public JrsVehicleProximitySelector proximitySelector;
    public JrsVehicleSelectionBillboard selectionBillboard;
    public JrsNewInputController inputController;
    
    [Header("Test Settings")]
    public bool enableDebugUI = true;
    public bool enableConsoleLogging = true;
    public KeyCode testKey = KeyCode.T;
    
    [Header("Debug Info")]
    [SerializeField] private bool systemInitialized = false;
    [SerializeField] private int currentVehicleIndex = -1;
    [SerializeField] private string currentVehicleName = "None";
    [SerializeField] private int nearbyVehicleCount = 0;
    [SerializeField] private string nearestVehicleName = "None";
    [SerializeField] private float distanceToNearest = 0f;
    [SerializeField] private bool billboardVisible = false;
    
    // UI Elements for runtime debugging
    private Canvas debugCanvas;
    private Text debugText;
    
    void Start()
    {
        InitializeReferences();
        if (enableDebugUI)
        {
            CreateDebugUI();
        }
        SubscribeToEvents();
    }
    
    void Update()
    {
        UpdateDebugInfo();
        
        if (Input.GetKeyDown(testKey))
        {
            RunSystemTest();
        }
        
        if (enableDebugUI && debugText != null)
        {
            UpdateDebugUI();
        }
    }
    
    private void InitializeReferences()
    {
        // Find references if not assigned
        if (vehicleSelector == null)
            vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
            
        if (proximitySelector == null)
            proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
            
        if (selectionBillboard == null)
            selectionBillboard = FindObjectOfType<JrsVehicleSelectionBillboard>();
            
        if (inputController == null)
            inputController = FindObjectOfType<JrsNewInputController>();
        
        // Check if system is properly initialized
        systemInitialized = vehicleSelector != null && proximitySelector != null && 
                           selectionBillboard != null && inputController != null;
        
        if (enableConsoleLogging)
        {
            Debug.Log($"VehicleProximitySelectionTester: System initialized = {systemInitialized}");
            Debug.Log($"- VehicleSelector: {(vehicleSelector != null ? "Found" : "Missing")}");
            Debug.Log($"- ProximitySelector: {(proximitySelector != null ? "Found" : "Missing")}");
            Debug.Log($"- SelectionBillboard: {(selectionBillboard != null ? "Found" : "Missing")}");
            Debug.Log($"- InputController: {(inputController != null ? "Found" : "Missing")}");
        }
    }
    
    private void SubscribeToEvents()
    {
        if (proximitySelector != null)
        {
            proximitySelector.OnNearestVehicleChanged += OnNearestVehicleChanged;
        }
        
        if (selectionBillboard != null)
        {
            selectionBillboard.OnBillboardClicked += OnBillboardClicked;
        }
    }
    
    private void OnNearestVehicleChanged(JrsVehicleController nearestVehicle)
    {
        if (enableConsoleLogging)
        {
            string vehicleName = nearestVehicle != null ? nearestVehicle.name : "None";
            Debug.Log($"Nearest vehicle changed to: {vehicleName}");
        }
    }
    
    private void OnBillboardClicked()
    {
        if (enableConsoleLogging)
        {
            Debug.Log("Billboard clicked!");
        }
    }
    
    private void UpdateDebugInfo()
    {
        if (!systemInitialized) return;
        
        // Update current vehicle info
        var currentVehicle = vehicleSelector.GetCurrentVehicle();
        currentVehicleIndex = vehicleSelector.GetCurrentVehicleIndex();
        currentVehicleName = currentVehicle != null ? currentVehicle.name : "None";
        
        // Update proximity info
        var nearbyVehicles = proximitySelector.GetNearbyVehicles();
        nearbyVehicleCount = nearbyVehicles.Count;
        
        var nearestVehicle = proximitySelector.NearestSelectableVehicle;
        nearestVehicleName = nearestVehicle != null ? nearestVehicle.name : "None";
        distanceToNearest = proximitySelector.GetDistanceToNearestVehicle();
        
        // Update billboard info
        billboardVisible = selectionBillboard.IsVisible;
    }
    
    private void CreateDebugUI()
    {
        // Create debug canvas
        GameObject canvasObj = new GameObject("VehicleProximityDebugCanvas");
        debugCanvas = canvasObj.AddComponent<Canvas>();
        debugCanvas.renderMode = RenderMode.ScreenSpaceOverlay;
        debugCanvas.sortingOrder = 1000;
        
        // Add CanvasScaler
        var scaler = canvasObj.AddComponent<CanvasScaler>();
        scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
        scaler.referenceResolution = new Vector2(1920, 1080);
        
        // Add GraphicRaycaster
        canvasObj.AddComponent<GraphicRaycaster>();
        
        // Create debug text
        GameObject textObj = new GameObject("DebugText");
        textObj.transform.SetParent(debugCanvas.transform, false);
        
        debugText = textObj.AddComponent<Text>();
        debugText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        debugText.fontSize = 16;
        debugText.color = Color.white;
        debugText.alignment = TextAnchor.UpperLeft;
        
        // Position text in top-left corner
        var rectTransform = textObj.GetComponent<RectTransform>();
        rectTransform.anchorMin = new Vector2(0, 1);
        rectTransform.anchorMax = new Vector2(0, 1);
        rectTransform.pivot = new Vector2(0, 1);
        rectTransform.anchoredPosition = new Vector2(10, -10);
        rectTransform.sizeDelta = new Vector2(400, 300);
        
        // Add background
        var background = textObj.AddComponent<Image>();
        background.color = new Color(0, 0, 0, 0.7f);
    }
    
    private void UpdateDebugUI()
    {
        if (debugText == null) return;
        
        debugText.text = $"Vehicle Proximity Selection Debug\n" +
                        $"System Initialized: {systemInitialized}\n" +
                        $"Press {testKey} to run test\n\n" +
                        $"Current Vehicle: {currentVehicleName} (Index: {currentVehicleIndex})\n" +
                        $"Nearby Vehicles: {nearbyVehicleCount}\n" +
                        $"Nearest Vehicle: {nearestVehicleName}\n" +
                        $"Distance to Nearest: {distanceToNearest:F1}m\n" +
                        $"Billboard Visible: {billboardVisible}\n" +
                        $"Detection Range: {(proximitySelector != null ? proximitySelector.DetectionRange.ToString("F1") + "m" : "N/A")}\n\n" +
                        $"Controls:\n" +
                        $"- Gamepad: SELECT button\n" +
                        $"- Mouse: Click billboard/vehicle\n" +
                        $"- Keyboard: Page Up/Down\n" +
                        $"- Test: {testKey} key";
    }
    
    private void RunSystemTest()
    {
        if (!systemInitialized)
        {
            Debug.LogError("System not properly initialized - cannot run test");
            return;
        }
        
        Debug.Log("=== Vehicle Proximity Selection System Test ===");
        
        // Test 1: Check vehicle list
        var vehicles = vehicleSelector.GetVehiclesList();
        Debug.Log($"Test 1 - Vehicle Count: {vehicles.Count}");
        
        // Test 2: Check current vehicle
        var currentVehicle = vehicleSelector.GetCurrentVehicle();
        Debug.Log($"Test 2 - Current Vehicle: {(currentVehicle != null ? currentVehicle.name : "None")}");
        
        // Test 3: Check proximity detection
        var nearbyVehicles = proximitySelector.GetNearbyVehicles();
        Debug.Log($"Test 3 - Nearby Vehicles: {nearbyVehicles.Count}");
        
        // Test 4: Check nearest vehicle
        var nearestVehicle = proximitySelector.NearestSelectableVehicle;
        Debug.Log($"Test 4 - Nearest Vehicle: {(nearestVehicle != null ? nearestVehicle.name : "None")}");
        
        // Test 5: Check selection capability
        bool canSelect = proximitySelector.CanSelectNearestVehicle();
        Debug.Log($"Test 5 - Can Select Nearest: {canSelect}");
        
        // Test 6: Check billboard visibility
        bool billboardVisible = selectionBillboard.IsVisible;
        Debug.Log($"Test 6 - Billboard Visible: {billboardVisible}");
        
        // Test 7: Try selection if possible
        if (canSelect)
        {
            Debug.Log("Test 7 - Attempting vehicle selection...");
            bool success = proximitySelector.SelectNearestVehicle();
            Debug.Log($"Test 7 - Selection Success: {success}");
        }
        else
        {
            Debug.Log("Test 7 - Skipped (no vehicle to select)");
        }
        
        Debug.Log("=== Test Complete ===");
    }
    
    void OnDestroy()
    {
        // Unsubscribe from events
        if (proximitySelector != null)
        {
            proximitySelector.OnNearestVehicleChanged -= OnNearestVehicleChanged;
        }
        
        if (selectionBillboard != null)
        {
            selectionBillboard.OnBillboardClicked -= OnBillboardClicked;
        }
        
        // Clean up debug UI
        if (debugCanvas != null)
        {
            DestroyImmediate(debugCanvas.gameObject);
        }
    }
    
    // Public methods for external testing
    public void TestProximityDetection()
    {
        if (proximitySelector != null)
        {
            var nearbyVehicles = proximitySelector.GetNearbyVehicles();
            Debug.Log($"Proximity test: Found {nearbyVehicles.Count} nearby vehicles");
            foreach (var vehicle in nearbyVehicles)
            {
                float distance = Vector3.Distance(
                    proximitySelector.transform.position, 
                    vehicle.transform.position);
                Debug.Log($"- {vehicle.name}: {distance:F1}m");
            }
        }
    }
    
    public void TestVehicleSelection()
    {
        if (proximitySelector != null && proximitySelector.CanSelectNearestVehicle())
        {
            proximitySelector.SelectNearestVehicle();
        }
        else
        {
            Debug.Log("No vehicle available for selection");
        }
    }
    
    public void ToggleProximitySelection()
    {
        if (vehicleSelector != null)
        {
            bool currentState = vehicleSelector.enableProximitySelection;
            vehicleSelector.SetProximitySelectionEnabled(!currentState);
            Debug.Log($"Proximity selection {(!currentState ? "enabled" : "disabled")}");
        }
    }
}
