using UnityEngine;
using System.Collections.Generic;
using System.Linq;

public class JrsVehicleProximitySelector : MonoBehaviour
{
    [Header("Proximity Detection Settings")]
    [SerializeField] private float detectionRange = 10f;
    [SerializeField] private float updateInterval = 0.1f;
    [SerializeField] private LayerMask vehicleLayerMask = -1;
    
    [Header("References")]
    [SerializeField] private JrsVehicleSelector vehicleSelector;
    [SerializeField] private Transform billboardUI;
    
    // Private variables
    private JrsVehicleController currentVehicle;
    private JrsVehicleController nearestSelectableVehicle;
    private List<JrsVehicleController> nearbyVehicles = new List<JrsVehicleController>();
    private float lastUpdateTime;
    private bool isInitialized = false;
    
    // Events
    public System.Action<JrsVehicleController> OnNearestVehicleChanged;

    // Public method to set vehicle selector reference
    public void SetVehicleSelector(JrsVehicleSelector selector)
    {
        vehicleSelector = selector;
        Debug.Log("Vehicle selector reference set in proximity selector");
    }

    // Public property to access detection range
    public float DetectionRange => detectionRange;

    // Public methods for debugging and external access
    public int GetNearbyVehicleCount() => nearbyVehicles.Count;
    public JrsVehicleController GetNearestSelectableVehicle() => nearestSelectableVehicle;
    
    public JrsVehicleController NearestSelectableVehicle => nearestSelectableVehicle;
    public float DetectionRange => detectionRange;
    public bool HasNearbyVehicles => nearbyVehicles.Count > 0;
    
    void Start()
    {
        Initialize();
    }
    
    void Update()
    {
        if (!isInitialized) return;
        
        // Update proximity detection at intervals
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateProximityDetection();
            lastUpdateTime = Time.time;
        }
    }
    
    private void Initialize()
    {
        // Find vehicle selector if not assigned
        if (vehicleSelector == null)
        {
            vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
            if (vehicleSelector == null)
            {
                Debug.LogError("JrsVehicleProximitySelector: No JrsVehicleSelector found in scene!");
                return;
            }
        }
        
        // Get current vehicle
        currentVehicle = vehicleSelector.GetCurrentVehicle();
        if (currentVehicle == null)
        {
            Debug.LogWarning("JrsVehicleProximitySelector: No current vehicle found. Will retry...");
            Invoke(nameof(Initialize), 1f);
            return;
        }
        
        isInitialized = true;
        Debug.Log("JrsVehicleProximitySelector initialized successfully");
    }
    
    private void UpdateProximityDetection()
    {
        // Update current vehicle reference
        var newCurrentVehicle = vehicleSelector.GetCurrentVehicle();
        if (newCurrentVehicle != currentVehicle)
        {
            currentVehicle = newCurrentVehicle;
            if (currentVehicle == null)
            {
                ClearNearbyVehicles();
                return;
            }
        }
        
        if (currentVehicle == null) return;
        
        // Find nearby vehicles
        FindNearbyVehicles();
        
        // Update nearest selectable vehicle
        UpdateNearestSelectableVehicle();
    }
    
    private void FindNearbyVehicles()
    {
        nearbyVehicles.Clear();
        
        Vector3 currentPosition = currentVehicle.transform.position;
        
        // Use OverlapSphere to find nearby colliders
        Collider[] nearbyColliders = Physics.OverlapSphere(currentPosition, detectionRange, vehicleLayerMask);
        
        foreach (Collider collider in nearbyColliders)
        {
            JrsVehicleController vehicle = collider.GetComponent<JrsVehicleController>();
            
            // Skip if not a vehicle or if it's the current vehicle
            if (vehicle == null || vehicle == currentVehicle) continue;
            
            // Check if vehicle is in the selector's vehicle list
            if (!vehicleSelector.vehicles.Contains(vehicle)) continue;
            
            // Check if vehicle is within detection range (double-check due to collider bounds)
            float distance = Vector3.Distance(currentPosition, vehicle.transform.position);
            if (distance <= detectionRange)
            {
                nearbyVehicles.Add(vehicle);
            }
        }
        
        // Sort by distance
        nearbyVehicles = nearbyVehicles.OrderBy(v => 
            Vector3.Distance(currentPosition, v.transform.position)).ToList();
    }
    
    private void UpdateNearestSelectableVehicle()
    {
        JrsVehicleController previousNearest = nearestSelectableVehicle;
        
        // Get the nearest vehicle (first in sorted list)
        nearestSelectableVehicle = nearbyVehicles.Count > 0 ? nearbyVehicles[0] : null;
        
        // Notify if nearest vehicle changed
        if (nearestSelectableVehicle != previousNearest)
        {
            OnNearestVehicleChanged?.Invoke(nearestSelectableVehicle);
            
            if (nearestSelectableVehicle != null)
            {
                float distance = Vector3.Distance(currentVehicle.transform.position, 
                    nearestSelectableVehicle.transform.position);
                Debug.Log($"Nearest selectable vehicle: {nearestSelectableVehicle.name} (Distance: {distance:F1}m)");
            }
            else
            {
                Debug.Log("No nearby selectable vehicles");
            }
        }
    }
    
    private void ClearNearbyVehicles()
    {
        nearbyVehicles.Clear();
        if (nearestSelectableVehicle != null)
        {
            nearestSelectableVehicle = null;
            OnNearestVehicleChanged?.Invoke(null);
        }
    }
    
    public bool CanSelectNearestVehicle()
    {
        return nearestSelectableVehicle != null && currentVehicle != null;
    }
    
    public bool SelectNearestVehicle()
    {
        if (!CanSelectNearestVehicle()) return false;
        
        int vehicleIndex = vehicleSelector.vehicles.IndexOf(nearestSelectableVehicle);
        if (vehicleIndex >= 0)
        {
            vehicleSelector.SwitchVehicle(vehicleIndex);
            Debug.Log($"Switched to vehicle: {nearestSelectableVehicle.name}");
            return true;
        }
        
        Debug.LogWarning("Failed to find vehicle index for selection");
        return false;
    }
    
    public List<JrsVehicleController> GetNearbyVehicles()
    {
        return new List<JrsVehicleController>(nearbyVehicles);
    }
    
    public float GetDistanceToNearestVehicle()
    {
        if (nearestSelectableVehicle == null || currentVehicle == null) return float.MaxValue;
        
        return Vector3.Distance(currentVehicle.transform.position, 
            nearestSelectableVehicle.transform.position);
    }
    
    // Public methods for configuration
    public void SetDetectionRange(float range)
    {
        detectionRange = Mathf.Max(0f, range);
    }
    
    public void SetUpdateInterval(float interval)
    {
        updateInterval = Mathf.Max(0.01f, interval);
    }
    
    // Debug visualization
    void OnDrawGizmosSelected()
    {
        if (currentVehicle != null)
        {
            Gizmos.color = Color.yellow;
            Gizmos.DrawWireSphere(currentVehicle.transform.position, detectionRange);
            
            if (nearestSelectableVehicle != null)
            {
                Gizmos.color = Color.green;
                Gizmos.DrawLine(currentVehicle.transform.position, 
                    nearestSelectableVehicle.transform.position);
                Gizmos.DrawWireCube(nearestSelectableVehicle.transform.position, Vector3.one * 2f);
            }
        }
    }
}
