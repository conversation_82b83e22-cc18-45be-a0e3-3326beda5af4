using UnityEngine;

/// <summary>
/// Simple, tested fix for Vehicle Proximity Selection System
/// Add this to any GameObject and press F8 to fix the system
/// </summary>
public class SimpleProximityFix : MonoBehaviour
{
    [Header("Settings")]
    public KeyCode fixKey = KeyCode.F8;
    
    void Update()
    {
        if (Input.GetKeyDown(fixKey))
        {
            FixProximitySystem();
        }
    }
    
    void Start()
    {
        // Auto-fix after 2 seconds
        Invoke(nameof(FixProximitySystem), 2f);
    }
    
    public void FixProximitySystem()
    {
        Debug.Log("=== FIXING VEHICLE PROXIMITY SYSTEM ===");
        
        // Find the vehicle selector
        JrsVehicleSelector vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
        if (vehicleSelector == null)
        {
            Debug.LogError("JrsVehicleSelector not found in scene!");
            return;
        }
        
        // Enable proximity selection
        vehicleSelector.enableProximitySelection = true;
        Debug.Log("✓ Enabled proximity selection");
        
        // Find or create proximity selector
        JrsVehicleProximitySelector proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        if (proximitySelector == null)
        {
            GameObject proximityObj = new GameObject("VehicleProximitySelector");
            proximitySelector = proximityObj.AddComponent<JrsVehicleProximitySelector>();
            Debug.Log("✓ Created proximity selector");
        }
        
        // Connect proximity selector to vehicle selector
        proximitySelector.SetVehicleSelector(vehicleSelector);
        vehicleSelector.proximitySelector = proximitySelector;
        Debug.Log("✓ Connected proximity selector");
        
        // Find or create billboard
        JrsVehicleSelectionBillboard billboard = FindObjectOfType<JrsVehicleSelectionBillboard>();
        if (billboard == null)
        {
            GameObject billboardObj = new GameObject("VehicleSelectionBillboard");
            billboard = billboardObj.AddComponent<JrsVehicleSelectionBillboard>();
            Debug.Log("✓ Created billboard");
        }
        
        // Connect billboard to vehicle selector
        vehicleSelector.selectionBillboard = billboard;
        Debug.Log("✓ Connected billboard");
        
        // Test the system
        TestSystem(vehicleSelector, proximitySelector, billboard);
        
        Debug.Log("=== FIX COMPLETE ===");
        Debug.Log("Drive near another vehicle to test!");
    }
    
    private void TestSystem(JrsVehicleSelector vehicleSelector, JrsVehicleProximitySelector proximitySelector, JrsVehicleSelectionBillboard billboard)
    {
        Debug.Log("--- Testing System ---");
        
        // Check current vehicle
        var currentVehicle = vehicleSelector.GetCurrentVehicle();
        if (currentVehicle == null)
        {
            Debug.LogWarning("No current vehicle - system may need more time to initialize");
            return;
        }
        
        Debug.Log($"Current vehicle: {currentVehicle.name}");
        Debug.Log($"Vehicle count: {vehicleSelector.vehicles.Count}");
        Debug.Log($"Detection range: {proximitySelector.DetectionRange}m");
        
        // Check for nearby vehicles
        int nearbyCount = proximitySelector.GetNearbyVehicleCount();
        Debug.Log($"Nearby vehicles: {nearbyCount}");
        
        if (nearbyCount > 0)
        {
            var nearest = proximitySelector.GetNearestSelectableVehicle();
            if (nearest != null)
            {
                float distance = Vector3.Distance(currentVehicle.transform.position, nearest.transform.position);
                Debug.Log($"Nearest: {nearest.name} at {distance:F1}m");
                
                if (distance <= proximitySelector.DetectionRange)
                {
                    Debug.Log("✓ Vehicle in range - billboard should appear!");
                }
            }
        }
        
        Debug.Log("--- Test Complete ---");
    }
}
