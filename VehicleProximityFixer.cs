using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

/// <summary>
/// Comprehensive fixer for Vehicle Proximity Selection System
/// This script will diagnose and fix common issues
/// </summary>
public class VehicleProximityFixer : MonoBehaviour
{
    [Header("Fix Settings")]
    public KeyCode fixKey = KeyCode.F9;
    public bool autoFixOnStart = true;
    
    [Header("Debug Settings")]
    public bool showDebugSphere = true;
    public bool verboseLogging = true;
    
    private JrsVehicleSelector vehicleSelector;
    private JrsVehicleProximitySelector proximitySelector;
    private JrsVehicleSelectionBillboard selectionBillboard;
    
    void Start()
    {
        if (autoFixOnStart)
        {
            Invoke(nameof(RunCompleteFix), 1f); // Wait for other systems to initialize
        }
    }
    
    void Update()
    {
        if (Input.GetKeyDown(fixKey))
        {
            RunCompleteFix();
        }
        

    }
    
    public void RunCompleteFix()
    {
        Debug.Log("=== RUNNING COMPLETE VEHICLE PROXIMITY FIX ===");
        
        // Step 1: Find all components
        FindAllComponents();
        
        // Step 2: Fix missing references and setup
        FixSystemReferences();
        
        // Step 3: Fix layer mask issues
        FixLayerMaskIssues();
        
        // Step 4: Fix billboard setup
        FixBillboardSetup();
        
        // Step 5: Test the system
        TestSystemFunctionality();
        
        Debug.Log("=== VEHICLE PROXIMITY FIX COMPLETE ===");
    }
    
    private void FindAllComponents()
    {
        Debug.Log("--- Finding System Components ---");
        
        vehicleSelector = FindObjectOfType<JrsVehicleSelector>();
        proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        selectionBillboard = FindObjectOfType<JrsVehicleSelectionBillboard>();
        
        Debug.Log($"Vehicle Selector: {(vehicleSelector != null ? "Found" : "Missing")}");
        Debug.Log($"Proximity Selector: {(proximitySelector != null ? "Found" : "Missing")}");
        Debug.Log($"Selection Billboard: {(selectionBillboard != null ? "Found" : "Missing")}");
    }
    
    private void FixSystemReferences()
    {
        Debug.Log("--- Fixing System References ---");
        
        if (vehicleSelector == null)
        {
            Debug.LogError("VehicleSelector not found! Please ensure JrsVehicleSelector is in the scene.");
            return;
        }
        
        // Ensure proximity selection is enabled
        if (!vehicleSelector.enableProximitySelection)
        {
            vehicleSelector.enableProximitySelection = true;
            Debug.Log("Enabled proximity selection on VehicleSelector");
        }
        
        // Create proximity selector if missing
        if (proximitySelector == null)
        {
            GameObject proximityObj = new GameObject("VehicleProximitySelector");
            proximitySelector = proximityObj.AddComponent<JrsVehicleProximitySelector>();
            vehicleSelector.proximitySelector = proximitySelector;
            Debug.Log("Created and connected ProximitySelector");
        }
        
        // Create billboard if missing
        if (selectionBillboard == null)
        {
            GameObject billboardObj = new GameObject("VehicleSelectionBillboard");
            selectionBillboard = billboardObj.AddComponent<JrsVehicleSelectionBillboard>();
            vehicleSelector.selectionBillboard = selectionBillboard;
            Debug.Log("Created and connected SelectionBillboard");
        }
        
        // Connect proximity selector to vehicle selector
        if (proximitySelector != null)
        {
            proximitySelector.SetVehicleSelector(vehicleSelector);
            Debug.Log("Connected ProximitySelector to VehicleSelector");
        }
    }
    
    private void FixLayerMaskIssues()
    {
        Debug.Log("--- Fixing Layer Mask Issues ---");
        
        if (proximitySelector == null) return;
        
        // Check if vehicles are on the correct layer
        var vehicles = vehicleSelector.vehicles;
        if (vehicles != null && vehicles.Count > 0)
        {
            Debug.Log($"Checking {vehicles.Count} vehicles for layer setup...");
            
            foreach (var vehicle in vehicles)
            {
                if (vehicle != null)
                {
                    int vehicleLayer = vehicle.gameObject.layer;
                    Debug.Log($"Vehicle {vehicle.name} is on layer {vehicleLayer} ({LayerMask.LayerToName(vehicleLayer)})");
                    
                    // Ensure vehicle has a collider
                    var collider = vehicle.GetComponent<Collider>();
                    if (collider == null)
                    {
                        // Add a box collider if missing
                        var boxCollider = vehicle.gameObject.AddComponent<BoxCollider>();
                        boxCollider.isTrigger = false;
                        Debug.Log($"Added BoxCollider to vehicle {vehicle.name}");
                    }
                }
            }
        }
    }
    
    private void FixBillboardSetup()
    {
        Debug.Log("--- Fixing Billboard Setup ---");
        
        if (selectionBillboard == null) return;
        
        // Ensure billboard has proper canvas setup
        var canvas = selectionBillboard.GetComponent<Canvas>();
        if (canvas == null)
        {
            canvas = selectionBillboard.gameObject.AddComponent<Canvas>();
            Debug.Log("Added Canvas to billboard");
        }
        
        // Setup canvas for world space
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = Camera.main;
        
        // Ensure canvas group exists
        var canvasGroup = selectionBillboard.GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = selectionBillboard.gameObject.AddComponent<CanvasGroup>();
            Debug.Log("Added CanvasGroup to billboard");
        }
        
        // Set initial visibility
        canvasGroup.alpha = 0f;
        canvasGroup.interactable = false;
        canvasGroup.blocksRaycasts = false;
        
        // Ensure GraphicRaycaster exists
        var raycaster = selectionBillboard.GetComponent<GraphicRaycaster>();
        if (raycaster == null)
        {
            raycaster = selectionBillboard.gameObject.AddComponent<GraphicRaycaster>();
            Debug.Log("Added GraphicRaycaster to billboard");
        }
        
        // Create UI elements if they don't exist
        CreateBillboardUI();
        
        Debug.Log("Billboard setup complete");
    }
    
    private void CreateBillboardUI()
    {
        // Create container
        Transform container = selectionBillboard.transform.Find("Container");
        if (container == null)
        {
            GameObject containerObj = new GameObject("Container");
            containerObj.transform.SetParent(selectionBillboard.transform, false);
            container = containerObj.transform;
            
            var rectTransform = containerObj.AddComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(200, 100);
            Debug.Log("Created billboard container");
        }
        
        // Create select image
        Transform imageTransform = container.Find("SelectImage");
        if (imageTransform == null)
        {
            GameObject imageObj = new GameObject("SelectImage");
            imageObj.transform.SetParent(container, false);
            var image = imageObj.AddComponent<Image>();
            
            var rectTransform = imageObj.GetComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(150, 75);
            rectTransform.anchoredPosition = Vector2.zero;
            
            // Load sprite
            Sprite selectSprite = Resources.Load<Sprite>("ui/select_to_enter");
            if (selectSprite != null)
            {
                image.sprite = selectSprite;
                Debug.Log("Loaded select_to_enter sprite");
            }
            else
            {
                // Create default white sprite
                Texture2D defaultTexture = new Texture2D(1, 1);
                defaultTexture.SetPixel(0, 0, Color.white);
                defaultTexture.Apply();
                image.sprite = Sprite.Create(defaultTexture, new Rect(0, 0, 1, 1), Vector2.one * 0.5f);
                Debug.LogWarning("Using default sprite - select_to_enter.png not found in Resources/ui/");
            }
            
            // Add button component
            var button = imageObj.AddComponent<Button>();
            Debug.Log("Created billboard UI elements");
        }
    }
    
    private void TestSystemFunctionality()
    {
        Debug.Log("--- Testing System Functionality ---");
        
        if (vehicleSelector == null || proximitySelector == null || selectionBillboard == null)
        {
            Debug.LogError("Cannot test - missing components");
            return;
        }
        
        var currentVehicle = vehicleSelector.GetCurrentVehicle();
        if (currentVehicle == null)
        {
            Debug.LogWarning("No current vehicle - system may not be fully initialized yet");
            return;
        }
        
        Debug.Log($"Current vehicle: {currentVehicle.name}");
        Debug.Log($"Detection range: {proximitySelector.DetectionRange}m");
        Debug.Log($"Nearby vehicles: {proximitySelector.GetNearbyVehicleCount()}");
        
        // Force a proximity check
        var nearestVehicle = proximitySelector.GetNearestSelectableVehicle();
        if (nearestVehicle != null)
        {
            float distance = Vector3.Distance(currentVehicle.transform.position, nearestVehicle.transform.position);
            Debug.Log($"Nearest vehicle: {nearestVehicle.name} at {distance:F1}m");
            
            if (distance <= proximitySelector.DetectionRange)
            {
                Debug.Log("Vehicle is within range - billboard should be visible");
            }
        }
        else
        {
            Debug.Log("No nearby vehicles detected");
        }
        
        Debug.Log("System test complete");
    }

    void OnDrawGizmos()
    {
        // Draw debug sphere around current vehicle
        if (showDebugSphere && proximitySelector != null && vehicleSelector != null)
        {
            var currentVehicle = vehicleSelector.GetCurrentVehicle();
            if (currentVehicle != null)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(currentVehicle.transform.position, proximitySelector.DetectionRange);
            }
        }
    }
}
