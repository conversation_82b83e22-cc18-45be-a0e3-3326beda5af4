using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class JrsVehicleSelectionBillboard : MonoBehaviour, IPointerClickHandler
{
    [Header("Billboard Settings")]
    [SerializeField] private float heightOffset = 3f;
    [SerializeField] private float scaleMultiplier = 1f;
    [SerializeField] private float minScale = 0.5f;
    [SerializeField] private float maxScale = 2f;
    [SerializeField] private float maxDistance = 20f;
    [SerializeField] private bool smoothTransitions = true;
    [SerializeField] private float transitionSpeed = 5f;
    
    [Header("UI References")]
    [SerializeField] private Canvas canvas;
    [SerializeField] private Image selectImage;
    [SerializeField] private Button selectButton;
    
    // Private variables
    private Camera mainCamera;
    private Transform targetVehicle;
    private JrsVehicleProximitySelector proximitySelector;
    private Vector3 targetPosition;
    private Vector3 targetScale;
    private bool isVisible = false;
    private CanvasGroup canvasGroup;
    
    // Events
    public System.Action OnBillboardClicked;
    
    public bool IsVisible => isVisible;
    public Transform TargetVehicle => targetVehicle;
    
    void Awake()
    {
        InitializeComponents();
    }
    
    void Start()
    {
        Initialize();
    }
    
    void Update()
    {
        if (isVisible && targetVehicle != null)
        {
            UpdateBillboardTransform();
            UpdateBillboardScale();
            UpdateBillboardRotation();
        }
    }
    
    private void InitializeComponents()
    {
        // Get or create canvas
        if (canvas == null)
        {
            canvas = GetComponent<Canvas>();
            if (canvas == null)
            {
                canvas = gameObject.AddComponent<Canvas>();
            }
        }
        
        // Setup canvas for world space
        canvas.renderMode = RenderMode.WorldSpace;
        canvas.worldCamera = Camera.main;
        
        // Get or create canvas group for fade effects
        canvasGroup = GetComponent<CanvasGroup>();
        if (canvasGroup == null)
        {
            canvasGroup = gameObject.AddComponent<CanvasGroup>();
        }
        
        // Setup canvas scaler
        var canvasScaler = GetComponent<CanvasScaler>();
        if (canvasScaler == null)
        {
            canvasScaler = gameObject.AddComponent<CanvasScaler>();
        }
        canvasScaler.uiScaleMode = CanvasScaler.ScaleMode.ConstantPixelSize;
        canvasScaler.scaleFactor = 1f;
        
        // Setup GraphicRaycaster for click detection
        var graphicRaycaster = GetComponent<GraphicRaycaster>();
        if (graphicRaycaster == null)
        {
            graphicRaycaster = gameObject.AddComponent<GraphicRaycaster>();
        }
        
        // Create UI elements if they don't exist
        CreateUIElements();
        
        // Initially hide the billboard
        SetVisibility(false);
    }
    
    private void CreateUIElements()
    {
        // Create main container if it doesn't exist
        Transform container = transform.Find("Container");
        if (container == null)
        {
            GameObject containerObj = new GameObject("Container");
            containerObj.transform.SetParent(transform, false);
            container = containerObj.transform;
            
            // Add RectTransform
            var rectTransform = containerObj.AddComponent<RectTransform>();
            rectTransform.sizeDelta = new Vector2(200, 100);
        }
        
        // Create select image if it doesn't exist
        if (selectImage == null)
        {
            Transform imageTransform = container.Find("SelectImage");
            if (imageTransform == null)
            {
                GameObject imageObj = new GameObject("SelectImage");
                imageObj.transform.SetParent(container, false);
                selectImage = imageObj.AddComponent<Image>();
                
                // Setup RectTransform
                var rectTransform = imageObj.GetComponent<RectTransform>();
                rectTransform.sizeDelta = new Vector2(150, 75);
                rectTransform.anchoredPosition = Vector2.zero;
            }
            else
            {
                selectImage = imageTransform.GetComponent<Image>();
            }
        }
        
        // Create select button if it doesn't exist
        if (selectButton == null)
        {
            selectButton = selectImage.gameObject.GetComponent<Button>();
            if (selectButton == null)
            {
                selectButton = selectImage.gameObject.AddComponent<Button>();
            }
        }
        
        // Load the select_to_enter image
        LoadSelectImage();
        
        // Setup button click event
        selectButton.onClick.RemoveAllListeners();
        selectButton.onClick.AddListener(OnSelectButtonClicked);
    }
    
    private void LoadSelectImage()
    {
        // Try to load the select_to_enter image from Resources
        Sprite selectSprite = Resources.Load<Sprite>("ui/select_to_enter");
        
        if (selectSprite == null)
        {
            // If not found in Resources, try to load from the ui folder
            // Note: In a real Unity project, you would typically load this through Resources or Addressables
            Debug.LogWarning("select_to_enter sprite not found in Resources. Using default sprite.");
            
            // Create a simple default sprite (white square)
            Texture2D defaultTexture = new Texture2D(1, 1);
            defaultTexture.SetPixel(0, 0, Color.white);
            defaultTexture.Apply();
            selectSprite = Sprite.Create(defaultTexture, new Rect(0, 0, 1, 1), Vector2.one * 0.5f);
        }
        
        if (selectImage != null)
        {
            selectImage.sprite = selectSprite;
            selectImage.color = Color.white;
        }
    }
    
    private void Initialize()
    {
        // Get main camera
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            Debug.LogError("JrsVehicleSelectionBillboard: Main camera not found!");
            return;
        }
        
        // Find proximity selector
        proximitySelector = FindObjectOfType<JrsVehicleProximitySelector>();
        if (proximitySelector == null)
        {
            Debug.LogError("JrsVehicleSelectionBillboard: JrsVehicleProximitySelector not found!");
            return;
        }
        
        // Subscribe to proximity selector events
        proximitySelector.OnNearestVehicleChanged += OnNearestVehicleChanged;
        
        // Update canvas camera reference
        canvas.worldCamera = mainCamera;
        
        Debug.Log("JrsVehicleSelectionBillboard initialized successfully");
    }
    
    private void OnNearestVehicleChanged(JrsVehicleController nearestVehicle)
    {
        if (nearestVehicle != null)
        {
            ShowBillboard(nearestVehicle.transform);
        }
        else
        {
            HideBillboard();
        }
    }
    
    public void ShowBillboard(Transform vehicle)
    {
        targetVehicle = vehicle;
        SetVisibility(true);
        
        // Set initial position
        UpdateBillboardTransform();
    }
    
    public void HideBillboard()
    {
        targetVehicle = null;
        SetVisibility(false);
    }
    
    private void SetVisibility(bool visible)
    {
        isVisible = visible;
        
        if (smoothTransitions && canvasGroup != null)
        {
            // Use CanvasGroup for smooth fade
            canvasGroup.alpha = visible ? 1f : 0f;
            canvasGroup.interactable = visible;
            canvasGroup.blocksRaycasts = visible;
        }
        else
        {
            // Direct visibility toggle
            gameObject.SetActive(visible);
        }
    }
    
    private void UpdateBillboardTransform()
    {
        if (targetVehicle == null) return;
        
        // Calculate target position above the vehicle
        Vector3 vehiclePosition = targetVehicle.position;
        targetPosition = vehiclePosition + Vector3.up * heightOffset;
        
        if (smoothTransitions)
        {
            transform.position = Vector3.Lerp(transform.position, targetPosition, 
                Time.deltaTime * transitionSpeed);
        }
        else
        {
            transform.position = targetPosition;
        }
    }
    
    private void UpdateBillboardScale()
    {
        if (targetVehicle == null || mainCamera == null) return;
        
        // Calculate distance-based scale
        float distance = Vector3.Distance(mainCamera.transform.position, transform.position);
        float normalizedDistance = Mathf.Clamp01(distance / maxDistance);
        float scale = Mathf.Lerp(maxScale, minScale, normalizedDistance) * scaleMultiplier;
        
        targetScale = Vector3.one * scale;
        
        if (smoothTransitions)
        {
            transform.localScale = Vector3.Lerp(transform.localScale, targetScale, 
                Time.deltaTime * transitionSpeed);
        }
        else
        {
            transform.localScale = targetScale;
        }
    }
    
    private void UpdateBillboardRotation()
    {
        if (mainCamera == null) return;
        
        // Make billboard face the camera
        Vector3 directionToCamera = mainCamera.transform.position - transform.position;
        directionToCamera.y = 0; // Keep billboard upright
        
        if (directionToCamera != Vector3.zero)
        {
            Quaternion targetRotation = Quaternion.LookRotation(-directionToCamera);
            
            if (smoothTransitions)
            {
                transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, 
                    Time.deltaTime * transitionSpeed);
            }
            else
            {
                transform.rotation = targetRotation;
            }
        }
    }
    
    private void OnSelectButtonClicked()
    {
        Debug.Log("Billboard select button clicked");
        OnBillboardClicked?.Invoke();
        
        // Trigger vehicle selection through proximity selector
        if (proximitySelector != null)
        {
            proximitySelector.SelectNearestVehicle();
        }
    }
    
    // IPointerClickHandler implementation for additional click detection
    public void OnPointerClick(PointerEventData eventData)
    {
        OnSelectButtonClicked();
    }
    
    void OnDestroy()
    {
        // Unsubscribe from events
        if (proximitySelector != null)
        {
            proximitySelector.OnNearestVehicleChanged -= OnNearestVehicleChanged;
        }
    }
    
    // Public configuration methods
    public void SetHeightOffset(float offset)
    {
        heightOffset = offset;
    }
    
    public void SetScaleMultiplier(float multiplier)
    {
        scaleMultiplier = multiplier;
    }
    
    public void SetTransitionSpeed(float speed)
    {
        transitionSpeed = speed;
    }
}
